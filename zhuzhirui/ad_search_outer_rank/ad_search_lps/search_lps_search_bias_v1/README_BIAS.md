# 搜索LPS Bias建模实现总结

## 实现概述

本实现严格按照要求为搜索LPS模型添加了bias建模功能，实现了不同网络使用不同特征的架构。

## 特征分配策略

### 严格的特征分离
- **主ctcvr网络**: 使用被mask的特征（移除35个搜索特征）
- **Bias网络**: 使用完整特征（包含所有搜索特征）
- **其他任务网络**: 使用完整特征

### 网络结构图

```
原始特征 ──┬─→ mask处理 ─→ 重建特征流程 ─→ 主CTCVR网络 ─→ main_logits
          │                                                    │
          └─→ 完整特征流程 ─┬─→ 其他任务网络 (pxr,ctr等)        │
                          │                                    ▼
                          └─→ Bias网络 ─→ bias_logits ─→ 融合 ─→ 最终输出
                              (含leave_seq_esmm)
```

## 核心实现

### 双特征流程构建

1. **保存原始特征**: `dnn_input_original = dnn_input`
2. **创建mask特征**: `dnn_input_masked = dnn_input * full_mask`
3. **构建两套特征流程**:
   - `dnn_input_final_masked`: 基于mask特征的完整处理流程
   - `dnn_input_final_full`: 基于原始特征的完整处理流程

### 网络输入分配

```python
# 主ctcvr网络：使用被mask的特征
ctcvr_input = dnn_input_final_masked

# 其他任务：使用完整特征
input = dnn_input_final_full

# Bias网络：使用完整特征
bias_input = dnn_input_final_full
```

### 被mask的搜索特征 (35个)

```
94, 96, 109, 115, 116, 117, 118, 119, 127, 199, 200, 201, 202, 203, 204, 205, 213,
235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 251, 254, 255, 256
```

### 关键差异

1. **搜索特征**: 主网络中35个搜索field被置0，bias网络中保持原值
2. **辅助任务**: `leave_seq_esmm_list`只在bias网络中使用
3. **网络结构**: 两个网络使用相同结构但独立参数

## 热启动支持

```python
# Bias网络参数从主网络热启动
bias_share_bottom_layer_* ← share_bottom_layer_*
bias_upper_layer_* ← upper_layer_*
```

## 配置参数

```python
self.enable_bias_network = True   # 启用bias网络
self.bias_weight = 0.5           # bias权重
```

## 预期效果

- **主网络**: 学习通用转化模式（无搜索bias）
- **Bias网络**: 学习搜索场景特有模式
- **融合结果**: 在搜索样本上表现更好，非搜索样本保持原有效果
